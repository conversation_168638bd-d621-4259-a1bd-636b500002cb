<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title><PERSON>ilam Login</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body { font-family: Arial, sans-serif; background: #f9f9f9; margin: 0; padding: 0; }
    .container { max-width: 350px; margin: 40px auto; background: #fff; padding: 24px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); }
    h2 { color: #10B981; text-align: center; }
    label { display: block; margin-bottom: 6px; }
    input { width: 100%; padding: 8px; margin-bottom: 16px; border-radius: 4px; border: 1px solid #ddd; }
    button { width: 100%; padding: 10px; background: #10B981; color: #fff; border: none; border-radius: 4px; font-size: 1rem; cursor: pointer; }
    .error { color: red; margin-bottom: 10px; text-align: center; }
  </style>
</head>
<body>
  <div class="container">
    <h2>Login to Shopilam</h2>
    <form id="loginForm">
      <label for="email">Email</label>
      <input type="email" id="email" required />
      <label for="password">Password</label>
      <input type="password" id="password" required />
      <button type="submit">Login</button>
      <div id="error" class="error"></div>
    </form>
  </div>
  <script>
    const SHOPILAM_API_SIGNIN = 'https://api1.shopilam.com/api/v1/auth/signin';
    document.getElementById('loginForm').addEventListener('submit', async function(e) {
      e.preventDefault();
      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;
      const errorDiv = document.getElementById('error');
      errorDiv.textContent = '';
      try {
        const res = await fetch(SHOPILAM_API_SIGNIN, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify({ email, password })
        });
        if (!res.ok) {
          const err = await res.json();
          errorDiv.textContent = err.message || 'Login failed';
          return;
        }
        const token = await res.text();
        // Send token to opener window
        if (window.opener) {
          window.opener.postMessage({ shopilam_token: token }, '*');
          window.close();
        } else {
          errorDiv.textContent = 'Could not send token to main window.';
        }
      } catch (err) {
        errorDiv.textContent = 'Network error';
      }
    });
  </script>
</body>
</html>
