<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title><PERSON><PERSON><PERSON></title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      margin: 0;
      padding: 20px;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .container {
      max-width: 400px;
      width: 100%;
      background: #fff;
      padding: 32px;
      border-radius: 16px;
      box-shadow: 0 10px 25px rgba(0,0,0,0.15);
      position: relative;
      overflow: hidden;
    }
    .container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #10B981, #059669);
    }
    .header {
      text-align: center;
      margin-bottom: 32px;
    }
    .logo {
      font-size: 48px;
      margin-bottom: 16px;
    }
    h2 {
      color: #1f2937;
      text-align: center;
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 700;
    }
    .subtitle {
      color: #6b7280;
      font-size: 14px;
      margin-bottom: 24px;
    }
    .form-group {
      margin-bottom: 20px;
    }
    label {
      display: block;
      margin-bottom: 8px;
      color: #374151;
      font-weight: 500;
      font-size: 14px;
    }
    input {
      width: 100%;
      padding: 12px 16px;
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      font-size: 16px;
      transition: border-color 0.2s ease, box-shadow 0.2s ease;
      box-sizing: border-box;
    }
    input:focus {
      outline: none;
      border-color: #10B981;
      box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }
    button {
      width: 100%;
      padding: 14px;
      background: linear-gradient(135deg, #10B981 0%, #059669 100%);
      color: #fff;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-top: 8px;
    }
    button:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }
    button:active {
      transform: translateY(0);
    }
    button:disabled {
      background: #9ca3af;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    .error {
      color: #dc2626;
      background: #fee2e2;
      border: 1px solid #fecaca;
      padding: 12px 16px;
      border-radius: 8px;
      margin-bottom: 16px;
      text-align: center;
      font-size: 14px;
    }
    .success {
      color: #059669;
      background: #d1fae5;
      border: 1px solid #a7f3d0;
      padding: 12px 16px;
      border-radius: 8px;
      margin-bottom: 16px;
      text-align: center;
      font-size: 14px;
    }
    .loading {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid #ffffff;
      border-radius: 50%;
      border-top-color: transparent;
      animation: spin 1s ease-in-out infinite;
      margin-right: 8px;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    .features {
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid #e5e7eb;
    }
    .features h3 {
      color: #374151;
      font-size: 16px;
      margin-bottom: 12px;
      text-align: center;
    }
    .feature-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    .feature-list li {
      color: #6b7280;
      font-size: 14px;
      margin-bottom: 8px;
      padding-left: 20px;
      position: relative;
    }
    .feature-list li::before {
      content: '✓';
      position: absolute;
      left: 0;
      color: #10B981;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">🚀</div>
      <h2>Welcome to Whatsopify</h2>
      <p class="subtitle">Unlock powerful WhatsApp features with your account</p>
    </div>

    <div id="error" class="error" style="display: none;"></div>
    <div id="success-message" class="success" style="display: none;"></div>

    <form id="loginForm">
      <div class="form-group">
        <label for="email">Email Address</label>
        <input type="email" id="email" required placeholder="Enter your email" />
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" required placeholder="Enter your password" />
      </div>

      <button type="submit" id="login-btn">
        <span id="login-text">Sign In</span>
      </button>
    </form>

    <div class="features">
      <h3>What you'll get access to:</h3>
      <ul class="feature-list">
        <li>Advanced contact management and import/export</li>
        <li>Bulk messaging and automated responses</li>
        <li>Custom chat filters and organization tools</li>
        <li>Enhanced sidebar with contact information</li>
        <li>Quick actions and productivity shortcuts</li>
      </ul>
    </div>
  </div>
  <script>
    const SHOPILAM_API_SIGNIN = 'https://api1.shopilam.com/api/v1/auth/signin';

    function showError(message) {
      const errorDiv = document.getElementById('error');
      const successDiv = document.getElementById('success-message');
      errorDiv.textContent = message;
      errorDiv.style.display = 'block';
      successDiv.style.display = 'none';
    }

    function showSuccess(message) {
      const errorDiv = document.getElementById('error');
      const successDiv = document.getElementById('success-message');
      successDiv.textContent = message;
      successDiv.style.display = 'block';
      errorDiv.style.display = 'none';
    }

    function hideMessages() {
      document.getElementById('error').style.display = 'none';
      document.getElementById('success-message').style.display = 'none';
    }

    function setLoading(isLoading) {
      const loginBtn = document.getElementById('login-btn');
      const loginText = document.getElementById('login-text');

      if (isLoading) {
        loginBtn.disabled = true;
        loginText.innerHTML = '<span class="loading"></span>Signing in...';
      } else {
        loginBtn.disabled = false;
        loginText.textContent = 'Sign In';
      }
    }

    document.getElementById('loginForm').addEventListener('submit', async function(e) {
      e.preventDefault();

      const email = document.getElementById('email').value.trim();
      const password = document.getElementById('password').value;

      if (!email || !password) {
        showError('Please fill in all fields');
        return;
      }

      hideMessages();
      setLoading(true);
      try {
        const res = await fetch(SHOPILAM_API_SIGNIN, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify({ email, password })
        });

        if (!res.ok) {
          const err = await res.json();
          setLoading(false);
          showError(err.message || 'Invalid email or password. Please try again.');
          return;
        }

        const token = await res.text();
        localStorage.setItem('shopilam_token', token);
        showSuccess('Login successful! Redirecting...');

        // Send token to opener window
        if (window.opener) {
          window.opener.postMessage({ shopilam_token: token }, '*');
          setTimeout(() => window.close(), 1500);
        } else {
          // Fallback for direct access
          window.postMessage({ type: 'LOGIN_SUCCESS', token: token }, '*');
          setTimeout(() => window.close(), 1500);
        }
      } catch (err) {
        setLoading(false);
        console.error('Login error:', err);
        showError('Network error. Please check your connection and try again.');
      }
    });

    // Clear error messages when user starts typing
    document.getElementById('email').addEventListener('input', hideMessages);
    document.getElementById('password').addEventListener('input', hideMessages);

    // Focus on email field when page loads
    document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('email').focus();
    });
  </script>
</body>
</html>
