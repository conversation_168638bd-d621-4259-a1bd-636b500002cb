
import { useEffect } from 'preact/hooks';
import { FaBell, FaCog } from "react-icons/fa";

const TopToolbar2 = () => {
  useEffect(() => {
    console.log("📌 TopToolbar mounted on WhatsApp Web (via Preact)");
  }, []);

  const handleClick = (label) => {
    console.log(`✅ ${label} button clicked`);
  };

  return (
    <div
      style={{
        height: '100%',
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingLeft: '1rem',
        paddingRight: '1rem',
        boxSizing: 'border-box',
        backgroundColor: 'white',
      }}
    >
      {/* Left side */}
      <div style={{ display: 'flex', gap: '1rem', overflowX: 'auto', paddingBottom: '4px' }}>
        {[
          { emoji: "🟢", label: "Inbox" },
          { emoji: "⭐", label: "Starred" },
          { emoji: "🤎", label: "Unread" },
          { emoji: "🤎", label: "Closed" },
          { emoji: "⏰", label: "Snoozed" },
          { emoji: "🚩", label: "Follow Up" },
          { emoji: "🏆", label: "VIP" },
          { emoji: "+", label: "Add" },
        ].map((item, idx) => (
          <button
            key={idx}
            onClick={() => handleClick(item.label)}
            style={{
              color: '#4a5568',
              padding: '4px 12px',
              borderRadius: '6px',
              transition: 'all 0.2s ease',
              fontSize: '0.875rem',
              whiteSpace: 'nowrap',
              backgroundColor: 'transparent',
              cursor: 'pointer',
              border: 'none',
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = '#f0f0f0';
              e.target.style.boxShadow = '0 2px 6px rgba(0,0,0,0.1)';
              e.target.style.transform = 'scale(1.05)';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = 'transparent';
              e.target.style.boxShadow = 'none';
              e.target.style.transform = 'scale(1)';
            }}
          >
            {item.emoji} {item.label}
          </button>
        ))}
      </div>

      {/* Right side */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
        <div style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#10B981' }}>Whatsopify</div>
        <button
          onClick={() => handleClick("Notifications")}
          style={{
            color: '#4a5568',
            transition: 'all 0.2s',
            background: 'transparent',
            border: 'none',
            cursor: 'pointer'
          }}
          onMouseEnter={(e) => e.target.style.color = '#2563EB'}
          onMouseLeave={(e) => e.target.style.color = '#4a5568'}
        >
          <FaBell size={20} />
        </button>
        <button
          onClick={() => handleClick("Settings")}
          style={{
            color: '#4a5568',
            transition: 'all 0.2s',
            background: 'transparent',
            border: 'none',
            cursor: 'pointer'
          }}
          onMouseEnter={(e) => e.target.style.color = '#2563EB'}
          onMouseLeave={(e) => e.target.style.color = '#4a5568'}
        >
          <FaCog size={20} />
        </button>
      </div>
    </div>
  );
};

export default TopToolbar2;
