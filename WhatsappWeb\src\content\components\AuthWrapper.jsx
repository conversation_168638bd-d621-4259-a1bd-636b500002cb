import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';

const SHOPILAM_API_SIGNIN = 'https://api1.shopilam.com/api/v1/auth/signin';
const SHOPILAM_API_VERIFY = 'https://api1.shopilam.com/api/v1/auth/verify';
const TOKEN_KEY = 'shopilam_token';
const USER_KEY = 'shopilam_user';

// Authentication Context
const AuthContext = createContext(null);

// Custom hook to use authentication
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Authentication Provider Component
export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [error, setError] = useState(null);

  // Verify token with server
  const verifyToken = useCallback(async (token) => {
    try {
      const response = await fetch(SHOPILAM_API_VERIFY, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
        setIsAuthenticated(true);
        localStorage.setItem(USER_KEY, JSON.stringify(userData));
        return true;
      } else {
        // Token is invalid
        localStorage.removeItem(TOKEN_KEY);
        localStorage.removeItem(USER_KEY);
        setIsAuthenticated(false);
        setUser(null);
        return false;
      }
    } catch (error) {
      console.error('Token verification failed:', error);
      // On network error, assume token is valid if it exists
      // This allows offline usage
      const storedUser = localStorage.getItem(USER_KEY);
      if (storedUser) {
        setUser(JSON.parse(storedUser));
        setIsAuthenticated(true);
        return true;
      }
      return false;
    }
  }, []);

  // Initialize authentication state
  useEffect(() => {
    const initAuth = async () => {
      setIsLoading(true);
      const token = localStorage.getItem(TOKEN_KEY);

      if (token) {
        await verifyToken(token);
      } else {
        setIsAuthenticated(false);
        setUser(null);
      }

      setIsLoading(false);
    };

    initAuth();

    // Listen for messages from login window
    const handleMessage = (event) => {
      if (event.data && event.data.shopilam_token) {
        localStorage.setItem(TOKEN_KEY, event.data.shopilam_token);
        verifyToken(event.data.shopilam_token);
        setError(null);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [verifyToken]);

  // Login function
  const login = useCallback(async (email, password) => {
    try {
      setError(null);
      const response = await fetch(SHOPILAM_API_SIGNIN, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password })
      });

      if (response.ok) {
        const token = await response.text();
        localStorage.setItem(TOKEN_KEY, token);
        await verifyToken(token);
        return { success: true };
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Login failed');
        return { success: false, error: errorData.message || 'Login failed' };
      }
    } catch (error) {
      const errorMessage = 'Network error. Please check your connection.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [verifyToken]);

  // Logout function
  const logout = useCallback(() => {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(USER_KEY);
    setIsAuthenticated(false);
    setUser(null);
    setError(null);
  }, []);

  // Get current token
  const getToken = useCallback(() => {
    return localStorage.getItem(TOKEN_KEY);
  }, []);

  // Check if user has specific permissions (can be extended)
  const hasPermission = useCallback((permission) => {
    if (!user) return false;
    // Add your permission logic here
    return true; // For now, authenticated users have all permissions
  }, [user]);

  const value = {
    isAuthenticated,
    isLoading,
    user,
    error,
    login,
    logout,
    getToken,
    hasPermission,
    verifyToken
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Enhanced AuthWrapper component
function AuthWrapper({ children }) {
  const { isAuthenticated, isLoading, error, logout } = useAuth();

  const handleLoginClick = () => {
    const loginUrl = window.location.origin + '/shopilam-login.html';
    window.open(
      loginUrl,
      'Shopilam Login',
      'width=450,height=600,scrollbars=yes,resizable=yes'
    );
  };

  if (isLoading) {
    return (
      <div style={loadingStyle}>
        <div style={spinnerStyle}></div>
        <p>Checking authentication...</p>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div style={loginContainerStyle}>
        <div style={loginCardStyle}>
          <h2 style={titleStyle}>🔐 Authentication Required</h2>
          <p style={descriptionStyle}>
            Please log in to Shopilam to access all WhatsApp extension features
          </p>
          {error && (
            <div style={errorStyle}>
              {error}
            </div>
          )}
          <button
            type="button"
            style={loginButtonStyle}
            onClick={handleLoginClick}
          >
            Login with Shopilam
          </button>
          <div style={featuresStyle}>
            <h3>🚀 Extension Features:</h3>
            <ul style={featureListStyle}>
              <li>📱 Start chats with unsaved contacts</li>
              <li>📊 Import/Export contact lists</li>
              <li>💬 Bulk messaging capabilities</li>
              <li>📋 Contact management tools</li>
              <li>🎯 Enhanced chat organization</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <button style={logoutButtonStyle} onClick={logout} title="Logout from Shopilam">
        🚪 Logout
      </button>
      {children}
    </div>
  );
}

// Styles for the enhanced authentication UI
const loadingStyle = {
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: '40px',
  textAlign: 'center',
  color: '#54656f'
};

const spinnerStyle = {
  width: '32px',
  height: '32px',
  border: '3px solid #f3f3f3',
  borderTop: '3px solid #10B981',
  borderRadius: '50%',
  animation: 'spin 1s linear infinite',
  marginBottom: '16px'
};

const loginContainerStyle = {
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  minHeight: '400px',
  padding: '20px',
  backgroundColor: '#f0f2f5'
};

const loginCardStyle = {
  backgroundColor: 'white',
  borderRadius: '12px',
  padding: '32px',
  maxWidth: '400px',
  width: '100%',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
  textAlign: 'center'
};

const titleStyle = {
  color: '#10B981',
  marginBottom: '16px',
  fontSize: '24px',
  fontWeight: 'bold'
};

const descriptionStyle = {
  color: '#54656f',
  marginBottom: '24px',
  lineHeight: '1.5'
};

const errorStyle = {
  backgroundColor: '#fee2e2',
  color: '#dc2626',
  padding: '12px',
  borderRadius: '6px',
  marginBottom: '16px',
  border: '1px solid #fecaca'
};

const loginButtonStyle = {
  width: '100%',
  padding: '12px 24px',
  fontSize: '16px',
  fontWeight: 'bold',
  background: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
  color: 'white',
  border: 'none',
  borderRadius: '8px',
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  marginBottom: '24px'
};

const featuresStyle = {
  textAlign: 'left',
  backgroundColor: '#f8fafc',
  padding: '20px',
  borderRadius: '8px',
  border: '1px solid #e2e8f0'
};

const featureListStyle = {
  margin: '12px 0 0 0',
  paddingLeft: '20px',
  color: '#64748b'
};

const logoutButtonStyle = {
  position: 'absolute',
  top: '10px',
  right: '10px',
  padding: '8px 16px',
  backgroundColor: '#ef4444',
  color: 'white',
  border: 'none',
  borderRadius: '6px',
  cursor: 'pointer',
  fontSize: '14px',
  fontWeight: '500',
  transition: 'background-color 0.2s ease',
  zIndex: 1000
};

// Add CSS animation for spinner
const style = document.createElement('style');
style.textContent = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  button:hover {
    opacity: 0.9;
    transform: translateY(-1px);
  }
`;
document.head.appendChild(style);

export default AuthWrapper;
