import React, { useEffect, useState } from 'react';

const SHOPILAM_API_SIGNIN = 'https://api1.shopilam.com/api/v1/auth/signin';
const TOKEN_KEY = 'shopilam_token';

function AuthWrapper({ children }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Check for token in localStorage
    const token = localStorage.getItem(TOKEN_KEY);
    setIsAuthenticated(!!token);

    // Listen for messages from login window
    const handleMessage = (event) => {
      // Accept messages from any origin for demo, restrict in production
      if (event.data && event.data.shopilam_token) {
        localStorage.setItem(TOKEN_KEY, event.data.shopilam_token);
        setIsAuthenticated(true);
      }
    };
    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  const handleLogout = () => {
    localStorage.removeItem(TOKEN_KEY);
    setIsAuthenticated(false);
  };

  if (!isAuthenticated) {
    // Show a button to open login window
    return (
      <div style={{ textAlign: 'center', marginTop: '50px' }}>
        <h2>Please log in to Shopilam to use this extension</h2>
        <button
          type="button"
          style={{ width: '100%', padding: '10px', fontSize: '1rem', background: '#10B981', color: 'white', border: 'none', borderRadius: '6px', cursor: 'pointer' }}
          onClick={() => {
            window.open(
              window.location.origin + '/shopilam-login.html',
              'Shopilam Login',
              'width=400,height=500'
            );
          }}
        >
          Login with Shopilam
        </button>
      </div>
    );
  }

  return (
    <div>
      <button style={{ position: 'absolute', top: 10, right: 10 }} onClick={handleLogout}>
        Logout
      </button>
      {children}
    </div>
  );
}

export default AuthWrapper;
