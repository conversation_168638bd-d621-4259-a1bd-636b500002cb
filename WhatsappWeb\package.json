{"name": "quick-notes-extension", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"fs-extra": "^11.3.0", "lodash": "^4.17.21", "papaparse": "^5.5.3", "preact": "^10.26.9", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.3"}}