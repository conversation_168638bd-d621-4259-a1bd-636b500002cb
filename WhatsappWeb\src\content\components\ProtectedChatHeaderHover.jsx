import React from 'react';
import { FeatureGuard } from './LoginGuard';
import ChatHeaderHover from './ChatHeaderHover';

/**
 * ProtectedChatHeaderHover Component
 * 
 * A wrapper component that protects the chat header hover with authentication
 */
const ProtectedChatHeaderHover = () => {
  return (
    <FeatureGuard 
      featureName="Chat Header Actions"
      icon="💬"
      description="Access quick actions and tools when hovering over chat headers."
    >
      <ChatHeaderHover />
    </FeatureGuard>
  );
};

export default ProtectedChatHeaderHover;
