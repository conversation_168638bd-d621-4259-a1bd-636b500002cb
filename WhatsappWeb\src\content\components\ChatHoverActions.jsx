import React, { useEffect } from 'react';
import './ChatListEnhancer.css';

const actionButtons = [
  {
    label: 'Mark as done',
    svg: `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M20.285 2.859l1.415 1.414-13.99 13.99-6.708-6.707 1.414-1.414 5.294 5.294z"/></svg>`,
    color: 'purple'
  },
  {
    label: 'Snooze',
    svg: `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm1-10h4v2h-6V7h2v5z"/></svg>`,
    color: '#e91e63'
  },
  {
    label: 'Archive',
    svg: `<svg viewBox="0 0 16 16" fill="currentColor"><path d="M3.3335 1.6665H12.6668C12.7703 1.6665 12.8743 1.6906 12.9668 1.73689..."/></svg>`,
    color: '#6c63ff'
  },
];

function injectActionButtons(chatNode) {
  if (chatNode.querySelector('.whatsopify-actions-container')) return;

  const container = document.createElement('div');
  container.className = 'whatsopify-actions-container';
  
  actionButtons.forEach(({ label, svg, color }) => {
    const btn = document.createElement('button');
    btn.className = 'whatsopify-action-btn';
    btn.innerHTML = svg;
    btn.title = label;
    btn.style.color = color;
    container.appendChild(btn);
  });

  // Insert in the same position as Cooby's buttons
  const chatRoot = chatNode.querySelector('[data-testid="cooby-chat-list-item-root"]') || 
                  chatNode.querySelector('._ak8j');
  if (chatRoot) {
    chatRoot.prepend(container);
  }
}

function injectStarButton(chatNode) {
  if (chatNode.querySelector('.whatsopify-star-btn')) return;

  const nameSpan = chatNode.querySelector('span[title][dir="auto"]');
  if (!nameSpan) return;
  
  const starBtn = document.createElement('button');
  starBtn.className = 'whatsopify-star-btn';
  starBtn.innerHTML = `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
      <path d="M12 18.26l-7.053 3.948 1.575-7.928L.587 8.792l8.027-.952L12 .5l3.386 7.34 8.027.952-5.935 5.488 1.575 7.928L12 18.26z"/>
    </svg>
  `;
  starBtn.title = 'Star this chat';
  nameSpan.parentNode.insertBefore(starBtn, nameSpan);
}

const ChatListEnhancer = () => {
  useEffect(() => {
    const processChats = () => {
      const chatItems = document.querySelectorAll('[role="listitem"]');
      chatItems.forEach(chat => {
        injectActionButtons(chat);
        injectStarButton(chat);
      });
    };

    // Initial injection
    processChats();

    // MutationObserver for dynamic content
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.addedNodes.length > 0) {
          processChats();
        }
      });
    });

    observer.observe(document.body, { 
      childList: true, 
      subtree: true 
    });

    return () => observer.disconnect();
  }, []);

  return null;
};

export default ChatListEnhancer;