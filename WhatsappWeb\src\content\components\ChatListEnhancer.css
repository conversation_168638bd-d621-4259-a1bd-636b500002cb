/* Star Button */
.whatsopify-star-btn {
  background: transparent;
  border: none;
  padding: 0 4px;
  margin-right: 8px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  display: inline-flex;
  align-items: center;
  transition: color 0.15s ease-in-out;
  vertical-align: middle;
}

.whatsopify-star-btn:hover {
  color: #ffc107;
}

.whatsopify-star-btn svg {
  width: 16px;
  height: 16px;
  display: block;
  pointer-events: none; /* ensures only button receives the click */
}
.whatsopify-star-btn svg {
  pointer-events: none; /* ensures only button receives the click */
}

/* Action Buttons Container */
.whatsopify-actions-container {
  display: none;
  position: absolute;
  top: 8px;
  right: 8px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 4px;
  gap: 4px;
  z-index: 100;
}

[role="listitem"]:hover .whatsopify-actions-container {
  display: flex;
}

/* Action Buttons */
.whatsopify-action-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.6);
  border-radius: 4px;
  transition: all 0.15s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.whatsopify-action-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: var(--color, inherit);
}

.whatsopify-action-btn svg {
  width: 16px;
  height: 16px;
  display: block;
}

/* Ensure chat items have relative positioning */
[role="listitem"] {
  position: relative !important;
}