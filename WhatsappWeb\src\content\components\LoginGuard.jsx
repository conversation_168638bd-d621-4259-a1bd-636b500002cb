import React from 'react';
import { useAuth } from './AuthWrapper';

/**
 * LoginGuard Component
 * 
 * A reusable component that protects any feature by checking authentication status.
 * If user is not authenticated, it shows a login prompt instead of the protected content.
 * 
 * Props:
 * - children: The content to protect
 * - fallback: Optional custom fallback component when not authenticated
 * - requirePermission: Optional permission string to check
 * - showLoginButton: Whether to show login button in fallback (default: true)
 * - message: Custom message to show when not authenticated
 */
const LoginGuard = ({ 
  children, 
  fallback = null, 
  requirePermission = null,
  showLoginButton = true,
  message = "Please log in to access this feature"
}) => {
  const { isAuthenticated, isLoading, hasPermission } = useAuth();

  // Show loading state
  if (isLoading) {
    return (
      <div style={loadingContainerStyle}>
        <div style={spinnerStyle}></div>
        <span style={loadingTextStyle}>Loading...</span>
      </div>
    );
  }

  // Check authentication
  if (!isAuthenticated) {
    // Use custom fallback if provided
    if (fallback) {
      return fallback;
    }

    // Default fallback UI
    return (
      <div style={guardContainerStyle}>
        <div style={guardContentStyle}>
          <div style={lockIconStyle}>🔒</div>
          <p style={guardMessageStyle}>{message}</p>
          {showLoginButton && (
            <button
              style={guardLoginButtonStyle}
              onClick={() => {
                const loginUrl = window.location.origin + '/shopilam-login.html';
                window.open(
                  loginUrl,
                  'Shopilam Login',
                  'width=450,height=600,scrollbars=yes,resizable=yes'
                );
              }}
            >
              Login to Continue
            </button>
          )}
        </div>
      </div>
    );
  }

  // Check permissions if required
  if (requirePermission && !hasPermission(requirePermission)) {
    return (
      <div style={guardContainerStyle}>
        <div style={guardContentStyle}>
          <div style={lockIconStyle}>⚠️</div>
          <p style={guardMessageStyle}>
            You don't have permission to access this feature.
          </p>
        </div>
      </div>
    );
  }

  // User is authenticated and has required permissions
  return children;
};

/**
 * FeatureGuard Component
 * 
 * A specialized version of LoginGuard for protecting specific features
 * with custom styling and messaging
 */
export const FeatureGuard = ({ 
  children, 
  featureName = "this feature",
  icon = "🔐",
  description = null
}) => {
  return (
    <LoginGuard
      message={`Please log in to use ${featureName}`}
      fallback={
        <div style={featureGuardStyle}>
          <div style={featureIconStyle}>{icon}</div>
          <h3 style={featureTitleStyle}>{featureName}</h3>
          {description && (
            <p style={featureDescriptionStyle}>{description}</p>
          )}
          <button
            style={featureLoginButtonStyle}
            onClick={() => {
              const loginUrl = window.location.origin + '/shopilam-login.html';
              window.open(
                loginUrl,
                'Shopilam Login',
                'width=450,height=600,scrollbars=yes,resizable=yes'
              );
            }}
          >
            Login to Access
          </button>
        </div>
      }
    >
      {children}
    </LoginGuard>
  );
};

/**
 * ButtonGuard Component
 * 
 * A specialized guard for protecting individual buttons
 * Shows a disabled state with tooltip when not authenticated
 */
export const ButtonGuard = ({ 
  children, 
  onClick,
  disabled = false,
  tooltip = "Login required to use this feature",
  ...buttonProps 
}) => {
  const { isAuthenticated } = useAuth();

  const handleClick = (e) => {
    if (!isAuthenticated) {
      e.preventDefault();
      e.stopPropagation();
      
      // Show login window
      const loginUrl = window.location.origin + '/shopilam-login.html';
      window.open(
        loginUrl,
        'Shopilam Login',
        'width=450,height=600,scrollbars=yes,resizable=yes'
      );
      return;
    }

    if (onClick) {
      onClick(e);
    }
  };

  return (
    <div style={{ position: 'relative', display: 'inline-block' }}>
      <button
        {...buttonProps}
        onClick={handleClick}
        disabled={disabled}
        title={!isAuthenticated ? tooltip : buttonProps.title}
        style={{
          ...buttonProps.style,
          opacity: !isAuthenticated ? 0.6 : 1,
          cursor: !isAuthenticated ? 'pointer' : (disabled ? 'not-allowed' : 'pointer')
        }}
      >
        {children}
        {!isAuthenticated && (
          <span style={lockOverlayStyle}>🔒</span>
        )}
      </button>
    </div>
  );
};

// Styles for the guard components
const loadingContainerStyle = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  padding: '20px',
  gap: '8px'
};

const spinnerStyle = {
  width: '16px',
  height: '16px',
  border: '2px solid #f3f3f3',
  borderTop: '2px solid #10B981',
  borderRadius: '50%',
  animation: 'spin 1s linear infinite'
};

const loadingTextStyle = {
  color: '#64748b',
  fontSize: '14px'
};

const guardContainerStyle = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  padding: '20px',
  backgroundColor: '#f8fafc',
  borderRadius: '8px',
  border: '1px solid #e2e8f0'
};

const guardContentStyle = {
  textAlign: 'center',
  maxWidth: '300px'
};

const lockIconStyle = {
  fontSize: '32px',
  marginBottom: '12px'
};

const guardMessageStyle = {
  color: '#64748b',
  marginBottom: '16px',
  fontSize: '14px',
  lineHeight: '1.5'
};

const guardLoginButtonStyle = {
  padding: '8px 16px',
  backgroundColor: '#10B981',
  color: 'white',
  border: 'none',
  borderRadius: '6px',
  cursor: 'pointer',
  fontSize: '14px',
  fontWeight: '500'
};

const featureGuardStyle = {
  padding: '24px',
  backgroundColor: 'white',
  borderRadius: '12px',
  border: '1px solid #e2e8f0',
  textAlign: 'center',
  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
};

const featureIconStyle = {
  fontSize: '48px',
  marginBottom: '16px'
};

const featureTitleStyle = {
  color: '#1f2937',
  marginBottom: '8px',
  fontSize: '18px',
  fontWeight: 'bold',
  textTransform: 'capitalize'
};

const featureDescriptionStyle = {
  color: '#64748b',
  marginBottom: '20px',
  fontSize: '14px',
  lineHeight: '1.5'
};

const featureLoginButtonStyle = {
  padding: '12px 24px',
  backgroundColor: '#10B981',
  color: 'white',
  border: 'none',
  borderRadius: '8px',
  cursor: 'pointer',
  fontSize: '16px',
  fontWeight: '600'
};

const lockOverlayStyle = {
  position: 'absolute',
  top: '2px',
  right: '2px',
  fontSize: '10px',
  backgroundColor: 'rgba(255, 255, 255, 0.9)',
  borderRadius: '50%',
  width: '16px',
  height: '16px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
};

export default LoginGuard;
