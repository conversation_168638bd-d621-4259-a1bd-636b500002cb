

import React, { useMemo } from 'react';

const getTheme = () => {
  // Try to detect WhatsApp dark mode from body class or style
  const body = document.body;
  const isDark = body.classList.contains('web') && body.classList.contains('dark') ||
    window.matchMedia('(prefers-color-scheme: dark)').matches;
  return isDark
    ? {
        bg: '#18191a',
        card: '#23272a',
        text: '#e4e6eb',
        subText: '#b0b3b8',
        accent: '#00bfae',
        border: '#333',
      }
    : {
        bg: '#f7f7f7',
        card: '#fff',
        text: '#222',
        subText: '#555',
        accent: '#00bfae',
        border: '#e2e8f0',
      };
};

const CatalogItem = ({ item }) => {
  const theme = getTheme();
  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      background: theme.card,
      borderRadius: '8px',
      boxShadow: '0 1px 4px rgba(0,0,0,0.07)',
      padding: '10px 14px',
      marginBottom: '10px',
      border: `1px solid ${theme.border}`,
    }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <span style={{ fontSize: '1.2em', color: theme.accent }}>🛒</span>
        <span style={{ color: theme.text, fontWeight: 500 }}>{item.name}</span>
      </div>
      <span style={{ color: theme.accent, fontWeight: 600 }}>{item.price}</span>
    </div>
  );
};

const InjectedSidebarContent = ({ contact, catalog, notes, onNotesChange }) => {
  const theme = useMemo(getTheme, []);
  return (
    <div style={{ padding: '20px', fontFamily: 'inherit', background: theme.bg, minHeight: '100vh' }}>
      <section style={{ marginBottom: '28px' }}>
        <h2 style={{ marginBottom: '12px', fontSize: '1.1rem', color: theme.text }}>Contact Info</h2>
        <div style={{ background: theme.card, borderRadius: '10px', boxShadow: '0 2px 8px rgba(0,0,0,0.07)', padding: '16px', fontSize: '0.98rem', color: theme.text, border: `1px solid ${theme.border}` }}>
          <div><strong>Name:</strong> <span style={{ color: contact?.name ? theme.text : theme.subText }}>{contact?.name || 'Not available'}</span></div>
          <div><strong>Phone:</strong> <span style={{ color: contact?.phone ? theme.text : theme.subText }}>{contact?.phone || 'Not available'}</span></div>
          <div><strong>About:</strong> <span style={{ color: contact?.email ? theme.text : theme.subText }}>{contact?.email || 'Not available'}</span></div>
        </div>
      </section>

      <section style={{ marginBottom: '28px' }}>
        <h2 style={{ marginBottom: '12px', fontSize: '1.1rem', color: theme.text }}>Catalog</h2>
        <div style={{ background: theme.card, borderRadius: '10px', boxShadow: '0 2px 8px rgba(0,0,0,0.07)', padding: '16px', border: `1px solid ${theme.border}` }}>
          {catalog && catalog.length > 0 ? (
            catalog.map((item, idx) => (
              <CatalogItem item={item} key={idx} />
            ))
          ) : (
            <div style={{ color: theme.subText }}>No products available.</div>
          )}
        </div>
      </section>

      <section>
        <h2 style={{ marginBottom: '12px', fontSize: '1.1rem', color: theme.text }}>Notes</h2>
        <div style={{ background: theme.card, borderRadius: '10px', boxShadow: '0 2px 8px rgba(0,0,0,0.07)', padding: '16px', border: `1px solid ${theme.border}` }}>
          <textarea
            style={{ width: '100%', minHeight: '80px', borderRadius: '6px', border: `1px solid ${theme.border}`, padding: '10px', resize: 'vertical', fontSize: '1rem', color: theme.text, background: theme.bg }}
            placeholder="Type your notes here..."
            value={notes || ''}
            onChange={e => onNotesChange && onNotesChange(e.target.value)}
          />
        </div>
      </section>
    </div>
  );
};

export default InjectedSidebarContent;
