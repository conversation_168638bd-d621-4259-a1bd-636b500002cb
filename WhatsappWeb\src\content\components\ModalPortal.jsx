import ReactDOM from "react-dom";

const ModalPortal = ({ children }) => {
  if (typeof window === "undefined") return null;

  let modalRoot = document.getElementById("modal-root");
  if (!modalRoot) {
    modalRoot = document.createElement("div");
    modalRoot.id = "modal-root";
    document.body.appendChild(modalRoot);
  }

  return ReactDOM.createPortal(children, modalRoot);
};

export default ModalPortal;
