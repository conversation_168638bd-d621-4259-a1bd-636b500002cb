import React, { useEffect } from "react";
import { AuthProvider } from "./components/AuthWrapper";
import AuthWrapper from "./components/AuthWrapper";
import Sidebar from "./components/InjectedSidebarButtons";
import TopToolbar from "./components/TopToolbar";
import ChatHeaderHover from "./components/ChatHeaderHover";

const App = () => {
  useEffect(() => {
    console.log("✅ Whatsapofy App mounted inside left sidebar successfully");
  }, []);

  return (
    <AuthProvider>
      <AuthWrapper>
        <Sidebar />
        <TopToolbar />
        <ChatHeaderHover />
      </AuthWrapper>
    </AuthProvider>
  );
};

export default App;