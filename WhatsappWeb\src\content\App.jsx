import React, { useEffect } from "react";
import AuthWrapper from "./components/AuthWrapper";
import Sidebar from "./components/InjectedSidebarButtons";
import TopToolbar from "./components/TopToolbar";
import ChatHeaderHover from "./components/ChatHeaderHover"; // Import the new component

const App = () => {
  useEffect(() => {
    console.log("✅ Whatsapofy App mounted inside left sidebar successfully");
  }, []);

  return (
    <AuthWrapper>
      <Sidebar />
      <TopToolbar />
      <ChatHeaderHover />
    </AuthWrapper>
  );
};

export default App;