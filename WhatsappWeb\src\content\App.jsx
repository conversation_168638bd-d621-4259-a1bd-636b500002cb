import React, { useEffect } from "react";
import Sidebar from "./components/InjectedSidebarButtons";
import TopToolbar from "./components/TopToolbar";
import ChatHeaderHover from "./components/ChatHeaderHover"; // Import the new component

const App = () => {
  useEffect(() => {
    console.log("✅ Whatsapofy App mounted inside left sidebar successfully");
  }, []);

  return (
    <>
      <Sidebar />
      <TopToolbar />
      <ChatHeaderHover /> {/* Render the new component for chat header functionality */}
      {/* You can remove ContactHoverCard from here if it's no longer used for the list item hover */}
    </>
  );
};

export default App;