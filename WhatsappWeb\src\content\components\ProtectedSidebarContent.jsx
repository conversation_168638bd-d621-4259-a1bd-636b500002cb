import React from 'react';
import { FeatureGuard } from './LoginGuard';
import InjectedSidebarContent from './InjectedSidebarContent';

/**
 * ProtectedSidebarContent Component
 * 
 * A wrapper component that protects the sidebar content with authentication
 */
const ProtectedSidebarContent = (props) => {
  return (
    <FeatureGuard 
      featureName="Contact Information Panel"
      icon="👤"
      description="View detailed contact information, notes, and catalog for the selected chat."
    >
      <InjectedSidebarContent {...props} />
    </FeatureGuard>
  );
};

export default ProtectedSidebarContent;
