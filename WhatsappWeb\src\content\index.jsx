// src/index.jsx
import React from 'react';
import { createRoot } from 'react-dom/client';
import { AuthProvider } from './components/AuthWrapper';
import ProtectedSidebarButtons from './components/ProtectedSidebarButtons';
import ProtectedTopToolbar from './components/ProtectedTopToolbar';
import ProtectedChatHeaderHover from './components/ProtectedChatHeaderHover';
import ProtectedChatListEnhancer from './components/ProtectedChatListEnhancer';
import ProtectedSidebarContent from './components/ProtectedSidebarContent';
import './App.css'; // Your main CSS file

console.log('🚀 Whatsapofy content script loaded at 12:30 PM PKT, 17/07/2025');

function waitForElement(selector, callback) {
    const el = document.querySelector(selector);
    if (el) {
        console.log(`✅ Element found immediately for selector: "${selector}"`);
        callback(el);
        return;
    }

    console.log(`⏳ Waiting for element with selector: "${selector}"`);
    const observer = new MutationObserver((mutations, obs) => {
        const element = document.querySelector(selector);
        if (element) {
            obs.disconnect();
            console.log(`✅ Element found by observer for selector: "${selector}"`);
            callback(element);
        }
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true,
    });
}

const TOOLBAR_HEIGHT = '48px'; // Assuming your TopToolbar has this height
const SIDEBAR_WIDTH = '300px'; // Define sidebar width once

// --- Sidebar Control State (GLOBAL to this content script) ---
let isSidebarOpen = false;
let sidebarRoot = null; // Store the React root for the sidebar
let mainAppContent = null; // Reference to the main WhatsApp content div that needs resizing
let sidebarProps = { contact: {}, catalog: [], notes: '', onNotesChange: null };
let lastActiveChatId = null;

// Function to toggle sidebar visibility and adjust WhatsApp UI
// This function is exposed globally to be called by React components
// Helper: Extract contact info from active chat
function getActiveChatDetails() {
    // WhatsApp Web DOM changes often; selectors may need adjustment
    const chatPanel = document.querySelector('div[role="main"]');
    if (!chatPanel) return {};
    // Get contact name
    const nameEl = chatPanel.querySelector('header span[title]');
    const name = nameEl ? nameEl.textContent : '';

    let phone = '';
    let about = '';

    // Try to open profile popup and extract info
    const profileBtn = chatPanel.querySelector('header span[title], header img');
    if (profileBtn) {
        profileBtn.click();
        // Wait for popup to appear
        const popupSelector = 'div[role="dialog"]';
        let tries = 0;
        const maxTries = 10;
        const interval = setInterval(() => {
            const popup = document.querySelector(popupSelector);
            if (popup) {
                // Extract phone and about
                // Phone: usually in a span or div with a phone number pattern
                const phoneEl = Array.from(popup.querySelectorAll('span, div')).find(el => el.textContent.match(/\+?\d[\d\s-]{7,}/));
                if (phoneEl) phone = phoneEl.textContent;
                // About: often in a div with status or description
                const aboutEl = Array.from(popup.querySelectorAll('div, span')).find(el => el.textContent && el.textContent.length > 0 && el.textContent !== name && (!phone || el.textContent !== phone));
                if (aboutEl) about = aboutEl.textContent;
                // Close popup (simulate ESC key)
                document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape', bubbles: true }));
                clearInterval(interval);
            } else if (++tries > maxTries) {
                clearInterval(interval);
            }
        }, 100);
    }

    return { name, phone, about };
}

// Example catalog (could be dynamic per contact)
function getCatalogForContact(contact) {
    // Replace with real logic if available
    return [
        { name: 'Product 1', price: '$10' },
        { name: 'Product 2', price: '$25' },
        { name: 'Product 3', price: '$40' },
    ];
}

// Notes storage (simple in-memory for now)
const notesMap = {};

function handleNotesChange(newNotes) {
    if (sidebarProps.contact && sidebarProps.contact.name) {
        notesMap[sidebarProps.contact.name] = newNotes;
        sidebarProps.notes = newNotes;
        if (sidebarRoot) {
            sidebarRoot.render(
                <AuthProvider>
                    <ProtectedSidebarContent {...sidebarProps} />
                </AuthProvider>
            );
        }
    }
}

// Listen for chat changes and update sidebar
function observeActiveChat() {
    const chatPanel = document.querySelector('div[role="main"]');
    if (!chatPanel) return;
    const config = { childList: true, subtree: true };
    const callback = () => {
        const contact = getActiveChatDetails();
        if (!contact.name || contact.name === lastActiveChatId) return;
        lastActiveChatId = contact.name;
        sidebarProps.contact = contact;
        sidebarProps.catalog = getCatalogForContact(contact);
        sidebarProps.notes = notesMap[contact.name] || '';
        sidebarProps.onNotesChange = handleNotesChange;
        if (sidebarRoot) {
            sidebarRoot.render(
                <AuthProvider>
                    <ProtectedSidebarContent {...sidebarProps} />
                </AuthProvider>
            );
        }
    };
    const observer = new MutationObserver(callback);
    observer.observe(chatPanel, config);
    // Initial call
    callback();
}

window.toggleWhatsappSidebar = (open) => {
    isSidebarOpen = typeof open === 'boolean' ? open : !isSidebarOpen;
    console.log(`Toggling sidebar: ${isSidebarOpen ? 'Open' : 'Closed'}`);

    if (!mainAppContent) {
        console.error('Main WhatsApp content container not found yet for sidebar adjustment!');
        return;
    }

    let sidebarContainer = document.getElementById('whatsapp-sidebar-root');

    if (isSidebarOpen) {
        // Open sidebar
        if (!sidebarContainer) {
            // Create and append sidebar container if it doesn't exist
            sidebarContainer = document.createElement('div');
            sidebarContainer.id = 'whatsapp-sidebar-root';
            Object.assign(sidebarContainer.style, {
                position: 'absolute',
                right: '0',
                top: '0',
                height: '100%',
                width: SIDEBAR_WIDTH,
                backgroundColor: '#f7f7f7',
                boxShadow: '-2px 0 5px rgba(0,0,0,0.1)',
                zIndex: '50',
                overflowY: 'auto',
                display: 'flex',
                flexDirection: 'column',
                boxSizing: 'border-box',
            });
            mainAppContent.appendChild(sidebarContainer);
            sidebarRoot = createRoot(sidebarContainer);
            // Initial sidebar props
            sidebarProps.contact = getActiveChatDetails();
            sidebarProps.catalog = getCatalogForContact(sidebarProps.contact);
            sidebarProps.notes = notesMap[sidebarProps.contact.name] || '';
            sidebarProps.onNotesChange = handleNotesChange;
            sidebarRoot.render(
                <AuthProvider>
                    <ProtectedSidebarContent {...sidebarProps} />
                </AuthProvider>
            );
            observeActiveChat();
            console.log('✅ InjectedSidebarContent rendered in sidebar container.');
        } else {
            sidebarContainer.style.display = 'flex';
            sidebarRoot.render(
                <AuthProvider>
                    <ProtectedSidebarContent {...sidebarProps} />
                </AuthProvider>
            );
            console.log('✅ Sidebar container shown and updated.');
        }

        mainAppContent.style.marginRight = SIDEBAR_WIDTH;
        console.log(`✅ Main WhatsApp content shifted left by ${SIDEBAR_WIDTH}.`);

    } else {
        // Close sidebar
        if (sidebarContainer) {
            sidebarContainer.style.display = 'none';
            console.log('✅ Sidebar hidden.');
        }

        // Restore main content width by removing the right margin
        mainAppContent.style.marginRight = '0px';
        console.log('✅ Main WhatsApp content restored to full width.');
    }
};


// Inject Top Toolbar
function injectTopToolbarIntoWhatsAppBody() {
    // The selector 'div.x78zum5.xdt5ytf.x5yr21d' is commonly the main layout container
    // of WhatsApp Web, holding both the chat list and chat panel.
    const whatsappMainBodyContainerSelector = 'div.x78zum5.xdt5ytf.x5yr21d';

    waitForElement(whatsappMainBodyContainerSelector, (whatsappMainBodyContainer) => {
        // Store this reference globally for sidebar management
        mainAppContent = whatsappMainBodyContainer;
        console.log('Main WhatsApp content container identified:', mainAppContent);

        // Check if toolbar already exists to prevent re-injection
        if (document.getElementById('whatsapp-top-toolbar-root')) {
            console.log('⚠️ Top Toolbar already injected. Skipping injection.');
            return;
        }

        console.log('Attempting to inject Top Toolbar into WhatsApp main body container...');

        const toolbarContainer = document.createElement('div');
        toolbarContainer.id = 'whatsapp-top-toolbar-root';

        Object.assign(toolbarContainer.style, {
            height: TOOLBAR_HEIGHT,
            width: '100%',
            boxSizing: 'border-box',
            backgroundColor: 'white',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            borderBottom: '1px solid #e2e8f0',
            // Position the toolbar so it respects the flow if mainAppContent is flex/grid
            flexShrink: '0', // Prevent it from shrinking
        });

        // Prepend toolbar to the main WhatsApp content container
        whatsappMainBodyContainer.prepend(toolbarContainer);

        console.log('✅ Top Toolbar container created and prepended.');

        const root = createRoot(toolbarContainer);
        root.render(
            <AuthProvider>
                <ProtectedTopToolbar />
            </AuthProvider>
        );
        console.log('✅ TopToolbar mounted as part of WhatsApp Web\'s flow.');

        // Initialize sidebar state to closed upon finding the main content
        window.toggleWhatsappSidebar(false);
    });
}

// Inject Sidebar Buttons (into WhatsApp's left panel)
function injectSidebarButtons() {
    // This selector targets the area above the chat list, where the profile pic and status icons are
    // It's a div with flex-grow: 1, often containing a <hr> element that we can insert before.
    const leftSidebarHeaderSelector = 'header[data-tab="2"] > div > div[style="flex-grow: 1;"]';

    waitForElement(leftSidebarHeaderSelector, (flexGrowParentDiv) => {
        if (document.getElementById('whatsapp-leftbar-buttons-root')) {
            console.log('⚠️ Sidebar buttons already injected. Skipping injection.');
            return;
        }

        // Find the HR element to insert before it, maintaining WhatsApp's layout
        const hrElement = flexGrowParentDiv.querySelector('hr.xjm9jq1');

        const container = document.createElement('div');
        container.id = 'whatsapp-leftbar-buttons-root';
        Object.assign(container.style, {
            marginTop: '8px',
            marginBottom: '8px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '4px', // Space between buttons
        });

        if (hrElement && hrElement.parentNode === flexGrowParentDiv) {
            // Insert before the HR element if found
            flexGrowParentDiv.insertBefore(container, hrElement);
            console.log('✅ Injected sidebar buttons inserted before HR element.');
        } else {
            // Fallback to appending if HR element is not found or structure changes
            flexGrowParentDiv.appendChild(container);
            console.warn('⚠️ HR element for sidebar buttons not found or not in expected position. Appending to end.');
            console.warn('Please inspect WhatsApp Web HTML for the correct HR element selector for precise placement.');
        }

        const root = createRoot(container);
        // Pass the globally exposed toggle function as a prop
        root.render(
            <AuthProvider>
                <ProtectedSidebarButtons onToggleSidebar={window.toggleWhatsappSidebar} />
            </AuthProvider>
        );
        console.log('✅ InjectedSidebarButtons mounted into left navbar.');
    });
}


// Inject ChatHeaderHover (floating element, needs 'body' as parent)
function injectChatHeaderHover() {
    waitForElement('body', (bodyElement) => {
        if (document.getElementById('whasapofy-chat-hover-root')) {
            console.log('⚠️ ChatHeaderHover already injected. Skipping injection.');
            return;
        }

        const chatHoverContainer = document.createElement('div');
        chatHoverContainer.id = 'whasapofy-chat-hover-root';
        // This element is intended to float, so appending to body is fine
        bodyElement.appendChild(chatHoverContainer);

        console.log('ChatHeaderHover container created and appended.');

        const root = createRoot(chatHoverContainer);
        root.render(
            <AuthProvider>
                <ProtectedChatHeaderHover />
            </AuthProvider>
        );
        console.log('✅ ChatHeaderHover mounted on WhatsApp Web.');
    });
}

// Inject ChatListEnhancer (floating element, needs 'body' as parent)
function injectChatListEnhancer() {
    waitForElement('body', (bodyElement) => {
        if (document.getElementById('whatsopify-chat-enhancer-root')) {
            console.log('⚠️ ChatListEnhancer already injected. Skipping injection.');
            return;
        }

        const enhancerContainer = document.createElement('div');
        enhancerContainer.id = 'whatsopify-chat-enhancer-root';
        // This element is intended to float, so appending to body is fine
        bodyElement.appendChild(enhancerContainer);

        console.log('ChatListEnhancer container created and appended.');

        const root = createRoot(enhancerContainer);
        root.render(
            <AuthProvider>
                <ProtectedChatListEnhancer />
            </AuthProvider>
        );
        console.log('✅ ChatListEnhancer mounted on WhatsApp Web.');
    });
}


// Call all injection functions
injectTopToolbarIntoWhatsAppBody();
injectSidebarButtons();
injectChatHeaderHover();
injectChatListEnhancer();

// Enhanced function to extract contact info from chat list item
function extractContactInfoFromChatDiv(chatDiv) {
    const contact = {};
    
    // Extract name from the span with title attribute
    const nameSpan = chatDiv.querySelector('span[title][dir="auto"]');
    if (nameSpan) {
        contact.name = nameSpan.textContent.trim();
        contact.title = nameSpan.getAttribute('title');
    }
    
    // Extract profile image
    const profileImg = chatDiv.querySelector('img[src*="whatsapp.net"]');
    if (profileImg) {
        contact.profilePicUrl = profileImg.src;
    }
    
    // Extract last message
    const lastMessageSpan = chatDiv.querySelector('span[dir="ltr"]');
    if (lastMessageSpan) {
        contact.lastMessage = lastMessageSpan.textContent.trim();
    }
    
    // Extract timestamp
    const timestampDiv = chatDiv.querySelector('._ak8i');
    if (timestampDiv && timestampDiv.textContent !== '') {
        contact.lastSeen = timestampDiv.textContent.trim();
    }
    
    // Check if message is read (double check marks)
    const doubleCheck = chatDiv.querySelector('[data-icon="status-dblcheck"]');
    contact.isRead = !!doubleCheck;
    
    return contact;
}

// Enhanced chat click observer
function observeChatClicks() {
    const chatListContainer = document.querySelector('[role="listbox"], [data-tab="2"]');
    if (!chatListContainer) {
        console.log('Chat list container not found, retrying...');
        setTimeout(observeChatClicks, 1000);
        return;
    }
    
    chatListContainer.addEventListener('click', (event) => {
        // Find the closest chat list item
        const chatItem = event.target.closest('[role="listitem"]');
        if (!chatItem) return;
        
        console.log('Chat clicked, extracting info...');
        
        // Extract contact info from the clicked chat
        const contactInfo = extractContactInfoFromChatDiv(chatItem);
        console.log('Extracted contact info:', contactInfo);
        
        // Update sidebar props with the new contact info
        sidebarProps.contact = contactInfo;
        sidebarProps.catalog = getCatalogForContact(contactInfo);
        sidebarProps.notes = notesMap[contactInfo.name] || '';
        sidebarProps.onNotesChange = handleNotesChange;
        
        // Auto-open sidebar when chat is clicked
        if (!isSidebarOpen) {
            window.toggleWhatsappSidebar(true);
        } else if (sidebarRoot) {
            // Update existing sidebar content
            sidebarRoot.render(
                <AuthProvider>
                    <ProtectedSidebarContent {...sidebarProps} />
                </AuthProvider>
            );
        }
        
        lastActiveChatId = contactInfo.name;
    });
    
    console.log('✅ Chat click observer attached');
}

// No explicit observer for the sidebar's presence in index.jsx needed now,
// as the toggleWhatsappSidebar function creates it if it doesn't exist
// and adjusts mainAppContent's margin.
