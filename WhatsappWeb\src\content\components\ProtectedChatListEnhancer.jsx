import React from 'react';
import { FeatureGuard } from './LoginGuard';
import ChatListEnhancer from './ChatListEnhancer';

/**
 * ProtectedChatListEnhancer Component
 * 
 * A wrapper component that protects the chat list enhancer with authentication
 */
const ProtectedChatListEnhancer = () => {
  return (
    <FeatureGuard 
      featureName="Chat List Enhancements"
      icon="📋"
      description="Enhanced chat list features for better organization and management."
    >
      <ChatListEnhancer />
    </FeatureGuard>
  );
};

export default ProtectedChatListEnhancer;
