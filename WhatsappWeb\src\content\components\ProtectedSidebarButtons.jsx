import React from 'react';
import { FeatureGuard } from './LoginGuard';
import InjectedSidebarButtons from './InjectedSidebarButtons';

/**
 * ProtectedSidebarButtons Component
 * 
 * A wrapper component that protects the sidebar buttons with authentication
 */
const ProtectedSidebarButtons = ({ onToggleSidebar }) => {
  return (
    <FeatureGuard 
      featureName="WhatsApp Extension Tools"
      icon="🛠️"
      description="Access advanced WhatsApp features including contact management, bulk messaging, and custom sidebar tools."
    >
      <InjectedSidebarButtons onToggleSidebar={onToggleSidebar} />
    </FeatureGuard>
  );
};

export default ProtectedSidebarButtons;
